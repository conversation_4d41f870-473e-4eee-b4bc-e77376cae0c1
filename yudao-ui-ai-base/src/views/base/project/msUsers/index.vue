<script lang="ts" setup>
import UsersModal from './usersModal.vue'
import UserImportModal from './userImportModal.vue'
import { columns, searchFormSchema } from './user.data'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import { useModal } from '@/components/Modal'
import { deleteUsers, exportUsers, getUsersPage, getTemplate } from '@/api/medsciUsers'
import { BasicTable, TableAction, useTable } from '@/components/Table'
import { IconEnum } from '@/enums/appEnum'
import { useRouter } from 'vue-router'

defineOptions({ name: 'MsUsers' })

const { t } = useI18n()
const { createConfirm, createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()
const [registerImportModal, { openModal: openImportModal }] = useModal()
const router = useRouter()
const [registerTable, { getForm, reload }] = useTable({
  title: '主站用户列表',
  api: getUsersPage,
  columns,
  formConfig: { labelWidth: 120, schemas: searchFormSchema },
  useSearchForm: true,
  showTableSetting: true,
  actionColumn: {
    width: 250,
    title: t('common.action'),
    dataIndex: 'action',
    fixed: 'right',
  }
})
function handleCreate() {
  openModal(true, { isUpdate: false })
}

function handleEdit(record: Recordable) {
  openModal(true, { record, isUpdate: true })
}
function handleLog(record: Recordable) {
  router.push(`/project/subscription/log?openid=${record.openid}&socialUserId=${record.socialUserId}`)
}

async function handleExport() {
  createConfirm({
    title: t('common.exportTitle'),
    iconType: 'warning',
    content: t('common.exportMessage'),
    async onOk() {
      await exportUsers(getForm().getFieldsValue())
      createMessage.success(t('common.exportSuccessText'))
    }
  })
}

async function handleDelete(record: Recordable) {
  await deleteUsers(record.id)
  createMessage.success(t('common.delSuccessText'))
  reload()
}
async function handleTemplate() {
  try {
    const res = await getTemplate()
    createMessage.success(t('common.exportSuccessText'))
  } catch (error) {
    console.error('模板下载失败:', error)
    createMessage.error('模板下载失败')
  }
}
function handleImport() {
  openImportModal(true)
}
</script>
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" v-auth="['medsci:users:create']" :preIcon="IconEnum.ADD" @click="handleCreate">
          {{ t('action.create') }}
        </a-button>
        <a-button v-auth="['medsci:users:export']" :preIcon="IconEnum.EXPORT" @click="handleExport">
          {{ t('action.export') }}
        </a-button>
        <a-button :preIcon="IconEnum.LOG" @click="handleTemplate">
          模板
        </a-button>
        <a-button :preIcon="IconEnum.IMPORT" @click="handleImport">
          {{ t('action.import') }}
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              { icon: IconEnum.LOG, label: '订阅链接日志',  onClick: handleLog.bind(null, record) },
              { icon: IconEnum.EDIT, label: t('action.edit'), auth: 'medsci:users:update', onClick: handleEdit.bind(null, record) },
              {
                icon: IconEnum.DELETE,
                danger: true,
                label: t('action.delete'),
                auth: 'medsci:users:delete',
                popConfirm: {
                  title: t('common.delMessage'),
                  placement: 'left',
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <UsersModal @register="registerModal" @success="reload()" />
    <UserImportModal @register="registerImportModal" @success="reload()" />
  </div>
</template>