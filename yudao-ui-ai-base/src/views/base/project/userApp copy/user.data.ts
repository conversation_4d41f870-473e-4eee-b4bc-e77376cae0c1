import type {BasicColumn, FormSchema} from '@/components/Table'
import {useRender} from '@/components/Table'
import {DICT_TYPE, getDictOptions} from '@/utils/dict'
import {formatToDate} from '@/utils/dateUtil'

export const columns: BasicColumn[] = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 60,
  },
  {
    title: '三方平台',
    dataIndex: 'socialType',
    width: 60,
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.AI_BASE_SOCIAL_TYPE)
    }
  },
  {
    title: '用户ID',
    dataIndex: 'socialUserId',
    width: 80,
  },
  {
    title: '用户名',
    dataIndex: 'userName',
    width: 160,
  },
  {
    title: '套餐',
    dataIndex: 'packageKey',
    width: 160,
  },
  {
    title: '类型',
    dataIndex: 'packageType',
    width: 160,
  },
  {
    title: '订阅状态',
    dataIndex: 'subStatus',
    width: 160,
  },
  {
    title: '订阅时间',
    dataIndex: 'subAt',
    width: 160,
    customRender: ({ text }) => {
      return useRender.renderDate(text, 'YYYY-MM-DD HH:MM:SS')
    }
  }, 
  {
    title: '订阅Logid',
    dataIndex: 'checkoutSessionId',
    width: 160,
  }, 
  {
    title: '退订时间',
    dataIndex: 'unsubAt',
    width: 160,
    customRender: ({ text }) => {
      return useRender.renderDate(text, 'YYYY-MM-DD HH:MM:SS')
    }
  }, 
  {
    title: '开始时间',
    dataIndex: 'startAt',
    width: 160,
    customRender: ({ text }) => {
      return useRender.renderDate(text, 'YYYY-MM-DD HH:MM:SS')
    }
  },
  {
    title: '过期时间',
    dataIndex: 'expireAt',
    width: 160,
    customRender: ({ text }) => {
      return useRender.renderDate(text, 'YYYY-MM-DD HH:MM:SS')
    }
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    width: 180,
    customRender: ({ text }) => {
      return useRender.renderDate(text)
    }
  },
  {
    title: '修改时间',
    dataIndex: 'updatedAt',
    width: 180,
    customRender: ({ text }) => {
      return useRender.renderDate(text, 'YYYY-MM-DD HH:MM:SS')
    }
  },
]

export const searchFormSchema: FormSchema[] = [
  {
    label: '三方平台',
    field: 'socialType',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.AI_BASE_SOCIAL_TYPE),
    },
    colProps: { span: 8 },
  },
  {
    label: '用户ID',
    field: 'socialUserId',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '套餐名',
    field: 'packageKey',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '类型',
    field: 'packageKey',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '过期时间',
    field: 'expiredAt',
    component: 'RangePicker',
    colProps: { span: 8 }
  },
]

export const createFormSchema: FormSchema[] = [  
  {
    label: '语言',
    field: 'locale',
    component: 'Select',
    componentProps: {
      options: [{'value': 'zh', 'label': '中文'}, {'value': 'en', 'label': '英文'}],
    },
    required: true,
  },
  {
    label: '三方平台',
    field: 'socialType',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.AI_BASE_SOCIAL_TYPE),
    },
    required: true,
  },
  {
    label: '用户ID',
    field: 'socialUserIdStr',
    component: 'InputTextArea',
    required: true,
    helpMessage: '格式 [123,456]，中间不要有空格',
    defaultValue: '[123,456]',
  },
  {
    label: '套餐',
    field: 'packageKey',
    component: 'Input',
    required: true,
  },
  {
    label: '类型',
    field: 'packageType',
    component: 'Select',
    componentProps: {
      options: [{'value': '月连续订阅', 'label': '月连续订阅'}, {'value': '周连续订阅', 'label': '周连续订阅'}],
    },
    required: true,
  },
  {
    label: '开始时间',
    field: 'startAt',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'x',
    },
  },
  {
    label: '截止时间',
    field: 'expireAt',
    required: true,
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'x',
    },
  },
]

export const updateFormSchema: FormSchema[] = [
  ...createFormSchema,
  {
    label: 'ID',
    field: 'id',
    component: 'Input',
    show: true,
  }
]