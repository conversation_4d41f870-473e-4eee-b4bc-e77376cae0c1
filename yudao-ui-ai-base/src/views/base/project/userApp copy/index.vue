<script lang="ts" setup>
import UsersModal from './usersModal.vue'
import { columns, searchFormSchema } from './user.data'
import { userPackagePage } from '@/api/common'
import { useI18n } from '@/hooks/web/useI18n'
import { BasicTable, TableAction, useTable } from '@/components/Table'
import { useMessage } from '@/hooks/web/useMessage'
import { useModal } from '@/components/Modal'
import { IconEnum } from '@/enums/appEnum'

defineOptions({ name: 'UserPackage' })

const { t } = useI18n()
const { createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()

const [registerTable, { getForm, reload }] = useTable({
  title: '中文订阅套餐',
  api: userPackagePage,
  formConfig: { labelWidth: 120, schemas: searchFormSchema },
  columns,
  useSearchForm: true,
  showTableSetting: true,
  actionColumn: {
    width: 250,
    title: t('common.action'),
    dataIndex: 'action',
    fixed: 'right',
  }
})

function handleCreate() {
  openModal(true, { isUpdate: false })
}

</script>
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" :preIcon="IconEnum.ADD" @click="handleCreate">
          导入
        </a-button>
      </template>
    </BasicTable>
    <UsersModal @register="registerModal" @success="reload()" />
  </div>
</template>