<script lang="ts" setup>
import { columns, searchFormSchema } from './user.data'
import { userAppPage,expireUserApp } from '@/api/common'
import { useI18n } from '@/hooks/web/useI18n'
import { BasicTable, TableAction, useTable } from '@/components/Table'
import { IconEnum } from '@/enums/appEnum'
import { useMessage } from '@/hooks/web/useMessage'

defineOptions({ name: 'UserPackage' })
const { createMessage } = useMessage()

const { t } = useI18n()

const [registerTable, {reload}] = useTable({
  title: '用户APP',
  api: userAppPage,
  formConfig: { labelWidth: 120, schemas: searchFormSchema },
  columns,
  useSearchForm: true,
  showTableSetting: true,
  actionColumn: {
    width: 250,
    title: t('common.action'),
    dataIndex: 'action',
    fixed: 'right',
  }
})

async function handleExpire(record: Recordable) {
  await expireUserApp({id:record.id})
  createMessage.success(t('common.delSuccessText'))
  reload()
}

</script>
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: IconEnum.RESET,
                danger: true,
                label: '过期',
                popConfirm: {
                  title: '是否确认手动过期用户APP？',
                  placement: 'left',
                  confirm: handleExpire.bind(null, record),
                },
                ifShow: () => {
                  return (record.status === 1)
                }
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </div>
</template>