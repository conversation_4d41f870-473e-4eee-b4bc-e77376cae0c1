import { h } from 'vue'
import type {BasicColumn, FormSchema} from '@/components/Table'
import {useRender} from '@/components/Table'
import {DICT_TYPE, getDictOptions} from '@/utils/dict'
import type { RenderCallbackParams } from '@/components/Form/src/types/form'

const lastNames =[
  "欧阳",
  "太史",
  "端木",
  "上官",
  "司马",
  "东方",
  "独孤",
  "南宫",
  "万俟",
  "闻人",
  "夏侯",
  "诸葛",
  "尉迟",
  "公羊",
  "赫连",
  "澹台",
  "皇甫",
  "宗政",
  "濮阳",
  "公冶",
  "太叔",
  "申屠",
  "公孙",
  "慕容",
  "仲孙",
  "钟离",
  "长孙",
  "宇文",
  "司徒",
  "鲜于",
  "司空",
  "闾丘",
  "子车",
  "亓官",
  "司寇",
  "巫马",
  "公西",
  "颛孙",
  "壤驷",
  "公良",
  "漆雕",
  "乐正",
  "宰父",
  "谷梁",
  "拓跋",
  "夹谷",
  "轩辕",
  "令狐",
  "段干",
  "百里",
  "呼延",
  "东郭",
  "南门",
  "羊舌",
  "微生",
  "公户",
  "公玉",
  "公仪",
  "梁丘",
  "公仲",
  "公上",
  "公门",
  "公山",
  "公坚",
  "左丘",
  "公伯",
  "西门",
  "公祖",
  "第五",
  "公乘",
  "贯丘",
  "公皙",
  "南荣",
  "东里",
  "东宫",
  "仲长",
  "子书",
  "子桑",
  "即墨",
  "达奚",
  "褚师",
  "吴铭"
]

export const columns: BasicColumn[] = [
  {
    title: 'ID',
    dataIndex: 'pkId',
    width: 100,
  },
  {
    title: '任务ID',
    dataIndex: 'id',
    width: 180,
  },
  {
    title: '父ID',
    dataIndex: 'pid',
    width: 180,
  },
  {
    title: '数据ID',
    dataIndex: 'dataId',
    width: 180,
  },
  {
    title: '总数',
    dataIndex: 'totalQuantity',
    width: 50,
  },
  {
    title: '数据类型',
    dataIndex: 'dataType',
    width: 120,
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.OCE_DATA_TYPE)
    }
  },
  {
    title: '数据值',
    dataIndex: 'taskParam',
    width: 500,
    customRender: ({ text }) => {
      if (!text) return '';
      try {
        const obj = JSON.parse(text);
        return h('div', { 
          style: { 
            maxHeight: '200px',
            overflowY: 'auto',
            textAlign: 'left'
          } 
        }, [
          h('pre', { 
            style: { 
              whiteSpace: 'pre-wrap', 
              wordBreak: 'break-all',
              margin: 0
            } 
          }, JSON.stringify(obj, null, 2))
        ]);
      } catch (e) {
        return text;
      }
    }
  },
  {
    title: '任务类型',
    dataIndex: 'taskType',
    width: 50,
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.OCE_TASK_TYPE)
    }
  },
  {
    title: '任务状态',
    dataIndex: 'taskStatus',
    width: 50,
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.OCE_TASK_STATUS)
    }
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 180,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 100,
    customRender: ({ text }) => {
      return useRender.renderDate(text)
    }
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 100,
    customRender: ({ text }) => {
      return useRender.renderDate(text)
    }
  },
  {
    title: 'dcr状态',
    dataIndex: 'dcrStatus',
    width: 80,
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.OCE_CHANGE_STATUS)
    }
  },
  {
    title: '操作类型',
    dataIndex: 'operationType',
    width: 180,
  },
  {
    title: '关联对象',
    dataIndex: 'mergeDataId',
    width: 180,
  },
]

export const searchFormSchema: FormSchema[] = [
  {
    label: '数据类型',
    field: 'dataType',
    component: 'Select',
    componentProps: {
        options: getDictOptions(DICT_TYPE.OCE_DATA_TYPE),
    },
    colProps: { span: 8 }
  },
  {
    label: '任务状态',
    field: 'taskStatus',
    component: 'Select',
    defaultValue: '处理中',
    componentProps: {
        options: getDictOptions(DICT_TYPE.OCE_TASK_STATUS, 'string'),
    },
    colProps: { span: 8 }
  },
  {
    label: 'ID-ID',
    field: 'pkId',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    label: '任务ID',
    field: 'taskId',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    label: '父ID',
    field: 'pid',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    label: '数据ID',
    field: 'dataId',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    label: '备注',
    field: 'remark',
    component: 'Input',
    colProps: { span: 8 }
  },
  {
    label: '创建时间',
    field: 'createTime',
    component: 'RangePicker',
    colProps: { span: 8 }
  },
  {
    label: '任务类型',
    field: 'taskType',
    component: 'Select',
    componentProps: {
        options: getDictOptions(DICT_TYPE.OCE_TASK_TYPE),
    },
    colProps: { span: 8 }
  },
  {
    label: 'dcr状态',
    field: 'dcrStatus',
    component: 'Select',
    componentProps: {
        options: getDictOptions(DICT_TYPE.OCE_CHANGE_STATUS, 'string'),
    },
    colProps: { span: 8 }
  },
]

export const dcrFormSchema: FormSchema[] = [
  {
    label: '编号',
    field: 'id',
    show: false,
    component: 'Input'
  },
  {
    label: '任务状态',
    field: 'taskStatus',
    required: true,
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.OCE_TASK_STATUS, 'number'),
    }
  },
  {
    label: '备注',
    field: 'remark',
    component: 'Input'
  },
  {
    label: 'dcr状态',
    field: 'dcrStatus',
    required: true,
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.OCE_CHANGE_STATUS, 'number'),
    }
  },
]

export const hcoFormSchema: FormSchema[] = [
  {
    label: '需要处理的hco_map数据',
    field: 'hco',
    ifShow: false,
    component: 'Input',
  },
  {
    label: '上次核查时间',
    field: 'lastDcrTime',
    component: 'Input',
    ifShow: ({ model }) => model.isOrigin == 2,
    render({ model }){
      return h('span', { style: { color: 'red', fontWeight: 'bold' } }, useRender.renderDate(model.lastDcrTime as string))
    },
    colProps: { span: 12 }
  },
  {
    label: '关联梅斯ID',
    field: 'mdmHcpId',
    ifShow: ({ model }) => model.isOrigin == 2,
    component: 'Input',
    render({ model }){
      return h('span', { style: { color: 'red', fontWeight: 'bold' } }, model.mdmHcoId)
    },
    colProps: { span: 12 }
  },
  {
    label: 'taskId',
    field: 'taskId',
    show: false,
    component: 'Input',
    colProps: { span: 12 }
  },
  {
    label: 'onekey医院ID',
    field: 'hcoId',
    show: false,
    component: 'Input',
    colProps: { span: 12 }
  },
  {
    label: '医院名称',
    field: 'hcoName',
    required: true,
    component: 'InputTextArea',
    suffix: ({model}: RenderCallbackParams): string => {
      if (model.isOrigin == 2) {
        return ''
      } else {
        let var1 = model?.hcoName
        let var2 = model?.hco?.hcoName
        if (var1 == var2) return ''
        return h('span', { style: { color: '#ff6c00'} }, '?') as any
      }
    },
    colProps: { span: 12 }
  },
  {
    label: '别名',
    field: 'hcoAlias',
    component: 'InputTextArea',
    helpMessage: '多个别名请用逗号(,)隔开 ',
    suffix: ({model}: RenderCallbackParams): string => {
      if (model.isOrigin == 2) {
        return ''
      } else {
        let var1 = model?.hcoAlias
        let var2 = model?.hco?.hcoAlias
        if (var1 == var2) return ''
        return h('span', { style: { color: '#ff6c00'} }, '?') as any
      }
    },
    colProps: { span: 12 }
  },
  {
    label: '省',
    field: 'province',
    component: 'Input',
    suffix: ({model}: RenderCallbackParams): string => {
      if (model.isOrigin == 2) {
        return ''
      } else {
        let var1 = model?.province
        let var2 = model?.hco?.province
        if (var1 == var2) return ''
        return h('span', { style: { color: '#ff6c00'} }, '?') as any
      }
    },
    colProps: { span: 12 }
  },
  {
    label: '市',
    field: 'city',
    component: 'Input',
    suffix: ({model}: RenderCallbackParams): string => {
      if (model.isOrigin == 2) {
        return ''
      } else {
        let var1 = model?.city
        let var2 = model?.hco?.city
        if (var1 == var2) return ''
        return '?'
      }
    },
    colProps: { span: 12 }
  },
  {
    label: '区',
    field: 'district',
    component: 'Input',
    suffix: ({model}: RenderCallbackParams): string => {
      if (model.isOrigin == 2) {
        return ''
      } else {
        let var1 = model?.district
        let var2 = model?.hco?.district
        if (var1 == var2) return ''
        return h('span', { style: { color: '#ff6c00'} }, '?') as any
      }
    },
    colProps: { span: 12 }
  },
  {
    label: '地址',
    field: 'address',
    component: 'Input',
    suffix: ({model}: RenderCallbackParams): string => {
      if (model.isOrigin == 2) {
        return ''
      } else {
        let var1 = model?.address
        let var2 = model?.hco?.address
        if (var1 == var2) return ''
        return h('span', { style: { color: '#ff6c00'} }, '?') as any
      }
    },
    colProps: { span: 12 }
  },
  {
    label: '邮编',
    field: 'zipCode',
    component: 'Input',
    suffix: ({model}: RenderCallbackParams): string => {
      if (model.isOrigin == 2) {
        return ''
      } else {
        let var1 = model?.zipCode
        let var2 = model?.hco?.zipCode
        if (var1 == var2) return ''
        return h('span', { style: { color: '#ff6c00'} }, '?') as any
      }
    },
    colProps: { span: 12 }
  },
  {
    label: '纬度',
    field: 'latitude',
    ifShow: ({ model }) => model.isOrigin == 1,
    component: 'Input',
    suffix: ({model}: RenderCallbackParams): string => {
      if (model.isOrigin == 2) {
        return ''
      } else {
        let var1 = model?.latitude
        let var2 = model?.hco?.latitude
        if (var1 == var2) return ''
        return h('span', { style: { color: '#ff6c00'} }, '?') as any
      }
    },
    colProps: { span: 12 }
  },
  {
    label: '经度',
    field: 'longitude',
    ifShow: ({ model }) => model.isOrigin == 1,
    component: 'Input',
    suffix: ({model}: RenderCallbackParams): string => {
      if (model.isOrigin == 2) {
        return ''
      } else {
        let var1 = model?.longitude
        let var2 = model?.hco?.longitude
        if (var1 == var2) return ''
        return h('span', { style: { color: '#ff6c00'} }, '?') as any
      }
    },
    colProps: { span: 12 }
  },
  {
    label: '高德地图坐标',
    field: 'longitude1',
    ifShow: ({ model }) => model.isOrigin == 2,
    component: 'Input',
    render(){
      return h('a', { href:'https://developer.amap.com/tools/picker', target: '_blank' }, '高德地图') 
    },
    colProps: { span: 12 }
  },
  {
    label: '经度-纬度',
    field: 'longiLati',
    ifShow: ({ model }) => model.isOrigin == 2,
    component: 'Input',
    helpMessage: '高德地图坐标，例116.401969,39.999664',
    colProps: { span: 12 }
  },
  {
    label: '等级',
    field: 'hcoGradeCode',
    component: 'Select',
    componentProps: {
      showSearch: true,
      optionFilterProp: "label",
      options: getDictOptions(DICT_TYPE.OCE_HCO_GRADE, 'string'),
    },
    suffix: ({model}: RenderCallbackParams): string => {
      if (model.isOrigin == 2) {
        return ''
      } else {
        let var1 = model?.hcoGradeCode
        let var2 = model?.hco?.hcoGradeCode
        if (var1 == var2) return ''
        return h('span', { style: { color: '#ff6c00'} }, '?') as any
      }
    },
    colProps: { span: 12 }
  },
  {
    label: '类型',
    field: 'hcoTypeCode',
    component: 'Select',
    componentProps: {
      showSearch: true,
      optionFilterProp: "label",
      options: getDictOptions(DICT_TYPE.OCE_HCO_TYPE, 'string'),
    },
    suffix: ({model}: RenderCallbackParams): string => {
      if (model.isOrigin == 2) {
        return ''
      } else {
        let var1 = model?.hcoTypeCode
        let var2 = model?.hco?.hcoTypeCode
        if (var1 == var2) return ''
        return h('span', { style: { color: '#ff6c00'} }, '?') as any
      }
    },
    colProps: { span: 12 }
  },
  {
    label: '电话',
    field: 'hcoConcat',
    component: 'Input',
    suffix: ({model}: RenderCallbackParams): string => {
      if (model.isOrigin == 2) {
        return ''
      } else {
        let var1 = model?.hcoConcat
        let var2 = model?.hco?.hcoConcat
        if (var1 == var2) return ''
        return h('span', { style: { color: '#ff6c00'} }, '?') as any
      }
    },
    colProps: { span: 12 }
  },
  {
    label: '传真',
    field: 'hcoFax',
    component: 'Input',
    suffix: ({model}: RenderCallbackParams): string => {
      if (model.isOrigin == 2) {
        return ''
      } else {
        let var1 = model?.Input
        let var2 = model?.hco?.Input
        if (var1 == var2) return ''
        return h('span', { style: { color: '#ff6c00'} }, '?') as any
      }
    },
    colProps: { span: 12 }
  },
  {
    label: '官网',
    field: 'hcoWebsite',
    component: 'Input',
    suffix: ({model}: RenderCallbackParams): string => {
      if (model.isOrigin == 2) {
        return ''
      } else {
        let var1 = model?.hcoWebsite
        let var2 = model?.hco?.hcoWebsite
        if (var1 == var2) return ''
        return h('span', { style: { color: '#ff6c00'} }, '?') as any
      }
    },
    colProps: { span: 12 }
  },
  {
    label: '公立/私立',
    field: 'hcoNature',
    component: 'Select',
    componentProps: {
      options: [
        { label: '公立', value: '公立' },
        { label: '私立', value: '私立' },
        { label: '未知', value: '未知' },
      ],
    },
    colProps: { span: 12 }
  },
  {
    label: '状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      showSearch: true,
      optionFilterProp: "label",
      options: getDictOptions(DICT_TYPE.OCE_HCO_STATUS, 'string'),
    },
    colProps: { span: 12 }
  },
  {
    label: 'isOrigin',
    field: 'isOrigin',
    show: false,
    component: 'Input',
    colProps: { span: 12 }
  },
  {
    label: '审核状态',
    field: 'approvalStatus',
    required: true,
    ifShow: ({ model }) => model.isOrigin == 2,
    component: 'Select',
    componentProps: {
      options: [
        { label: '批准', value: 'Approved' },
        { label: '拒绝', value: 'Rejected' },
      ],
    },
    helpMessage: "拒绝 不会修改原数据",
    colProps: { span: 12 }
  },
  {
    label: '常用拒绝备注',
    field: 'approvalRemarks1',
    ifShow: ({ model }) => model.isOrigin == 2,
    component: 'Select',
    componentProps: {
      showSearch: true,
      optionFilterProp: "label",
      options: [
        {
            "label": "无",
            "value": "-"
        },
        {
            "label": "R10E 信息无效（超出可验证范围）-核实非同一家医院，医院地址不可变更",
            "value": "R10E 信息无效（超出可验证范围）-核实非同一家医院，医院地址不可变更"
        },
        {
            "label": "R10E 信息无效（超出可验证范围）-提交的医院名和地址有出入（医院名是总院，地址是分院）",
            "value": "R10E 信息无效（超出可验证范围）-提交的医院名和地址有出入（医院名是总院，地址是分院）"
        },
        {
            "label": "R10E 信息无效（超出可验证范围）-提交医院地址错误,医院名不能填写在省、市、区内",
            "value": "R10E 信息无效（超出可验证范围）-提交医院地址错误,医院名不能填写在省、市、区内"
        },
        {
            "label": "R10E 信息无效（超出可验证范围）-提交医院地址错误,实际在xx区",
            "value": "R10E 信息无效（超出可验证范围）-提交医院地址错误,实际在xx区"
        },
      ],
    },
    helpMessage: "常用拒绝备注 审核备注 会合并在一起为审核备注",
    colProps: { span: 24 }
  },
  {
    label: '审核备注',
    field: 'approvalRemarks',
    ifShow: ({ model }) => model.isOrigin == 2,
    component: 'InputTextArea',
    helpMessage: "审核备注和核验的来源，如网址；拒绝必填",
    colProps: { span: 24 }
  },
  {
    label: '注意',
    field: 'xxxx',
    ifShow: ({ model }) => model.isOrigin == 2,
    component: 'Input',
    render(){
      return h('span', { style: { color: 'red' } }, '以下是非必填，如选择 重复 则必须选择 关联对象')
    },
    colProps: { span: 24 }
  },
  {
    label: '操作类型',
    field: 'operationType',
    ifShow: ({ model }) => model.isOrigin == 2,
    component: 'Select',
    componentProps: {
      options: [
        { label: '2.重复', value: '2' },
      ],
    },
    helpMessage: "选择操作类型时 审核状态=批准。如果默认显示 重复，需要代表核实下方管理对象是否正确后再提交",
    colProps: { span: 12 }
  },
]

export const hcpFormSchema: FormSchema[] = [
  {
    label: '需要处理的hcp_map数据',
    field: 'hcp',
    ifShow: false,
    component: 'Input',
  },
  {
    label: '上次核查时间',
    field: 'lastDcrTime',
    ifShow: ({ model }) => model.isOrigin == 2,
    component: 'Input',
    render({ model }){
      return h('span', { style: { color: 'red', fontWeight: 'bold' } }, useRender.renderDate(model.lastDcrTime as string))
    },
    colProps: { span: 12 }
  },
  {
    label: '关联梅斯ID',
    field: 'mdmHcpId',
    ifShow: ({ model }) => model.isOrigin == 2,
    component: 'Input',
    render({ model }){
      return model.mdmHcpId
    },
    colProps: { span: 12 }
  },
  {
    label: '医院名称',
    field: 'hcoName',
    required: true,
    component: 'Input',
    render({ model }){
      return model.hcoName
    },
    suffix: ({model}: RenderCallbackParams): string => {
      if (model.isOrigin == 2) {
        return ''
      } else {
        let var1 = model?.hcoName
        let var2 = model?.hcp?.hcoName
        if (var1 == var2) return ''
        return h('span', { style: { color: '#ff6c00'} }, '?') as any
      }
    },
    colProps: { span: 12 }
  },
  {
    label: 'taskId',
    field: 'taskId',
    show: false,
    component: 'Input',
    colProps: { span: 12 }
  },
  {
    label: 'onekeyID',
    field: 'hcpId',
    show: false,
    component: 'Input',
    colProps: { span: 12 }
  },
  {
    label: '科室ID',
    field: 'departmentId',
    show: false,
    component: 'Input',
    colProps: { span: 12 }
  },
  {
    label: '科室名称',
    field: 'departmentCode',
    required: true,
    component: 'Select',
    componentProps: {
      showSearch: true,
      // labelInValue: true,
      optionFilterProp: "label",
      options: getDictOptions(DICT_TYPE.OCE_DEPARTMENT, 'string'),
    },
    suffix: ({model}: RenderCallbackParams): string => {
      if (model.isOrigin == 2) {
        return ''
      } else {
        let var1 = model?.departmentCode
        let var2 = model?.hcp?.departmentCode
        if (var1 == var2) return ''
        return h('span', { style: { color: '#ff6c00'} }, '?') as any
      }
    },
    colProps: { span: 12 }
  },
  {
    label: '姓',
    field: 'lastName',
    component: 'Input',
    suffix: ({model}: RenderCallbackParams): string => {
      if (model.isOrigin == 2) {
        let var1 = model?.lastName
        // var1 = '欧阳'
        console.log(lastNames.includes(var1))
        if (var1.length==2&&!lastNames.includes(var1)) {
          return h('span', { style: { color: '#ff6c00'} }, '非复姓') as any
        }
        return ''
      } else {
        let var1 = model?.lastName
        let var2 = model?.hcp?.lastName
        if (var1 == var2) return ''
        return h('span', { style: { color: '#ff6c00'} }, '?') as any
      }
    },
    colProps: { span: 12 }
  },
  {
    label: '名',
    field: 'firstName',
    component: 'Input',
    suffix: ({model}: RenderCallbackParams): string => {
      if (model.isOrigin == 2) {
        return ''
      } else {
        let var1 = model?.firstName
        let var2 = model?.hcp?.firstName
        if (var1 == var2) return ''
        return h('span', { style: { color: '#ff6c00'} }, '?') as any
      }
    },
    colProps: { span: 12 }
  },
  {
    label: '姓名',
    field: 'hcpName',
    required: true,
    component: 'Input',
    render({ model }){
      return (model.lastName?? '')+(model.firstName?? '')
    },
    suffix: ({model}: RenderCallbackParams): string => {
      if (model.isOrigin == 2) {
        let hcoName = model?.hcoName
        let hcpName = (model.lastName?? '')+(model.firstName?? '')
        let keyword = hcoName+ ' '+hcpName
        return h('div', {}, [
          h('a', { href: 'https://www.baidu.com/s?wd=' + keyword, target: '_blank', style: { marginRight: '10px' } }, '百度') ,
          h('a', { href: 'http://10.0.0.67:6582/mdm/hcp-check-search?hco_name='+hcoName+'&hcp_name=' +  hcpName, target: '_blank' }, '本地'),
        ])  as any
      } else {
        let var1 = model?.hcpName
        let var2 = model?.hcp?.hcpName
        if (var1 == var2) return ''
        return h('span', { style: { color: '#ff6c00'} }, '?') as any
      }
    },
    colProps: { span: 12 }
  },
  {
    label: '手机号',
    field: 'mobile',
    component: 'Input',
    render({ model }){
      return model.mobile
    },
    suffix: ({model}: RenderCallbackParams): string => {
      if (model.isOrigin == 2) {
        return ''
      } else {
        let var1 = model?.mobile
        let var2 = model?.hcp?.mobile
        if (var1 == var2) return ''
        return h('span', { style: { color: '#ff6c00'} }, '?') as any
      }
    },
    colProps: { span: 12 }
  },
  {
    label: '性别',
    field: 'genderCode',
    component: 'Select',
    componentProps: {
        options: getDictOptions(DICT_TYPE.OCE_HCP_GENDER, 'string'),
    },
    suffix: ({model}: RenderCallbackParams): string => {
      if (model.isOrigin == 2) {
        return ''
      } else {
        let var1 = model?.genderCode
        let var2 = model?.hcp?.genderCode
        if (var1 == var2) return ''
        return h('span', { style: { color: '#ff6c00'} }, '?') as any
      }
    },
    colProps: { span: 12 }
  },
  {
    label: '职称',
    field: 'hcpProfessionalCode',
    component: 'Select',
    componentProps: {
      showSearch: true,
      optionFilterProp: "label",
      options: getDictOptions(DICT_TYPE.OCE_HCP_PROFESSIONAL, 'string'),
      onChange: (value, a) => {
        console.log(value, a)
      },
    },
    suffix: ({model}: RenderCallbackParams): string => {
      if (model.isOrigin == 2) {
        return ''
      } else {
        let var1 = model?.hcpProfessionalCode
        let var2 = model?.hcp?.hcpProfessionalCode
        if (var1 == var2) return ''
        return h('span', { style: { color: '#ff6c00'} }, '?') as any
      }
    },
    colProps: { span: 12 }
  },
  {
    label: '职务',
    field: 'hcpPositionCode',
    component: 'Select',
    componentProps: {
      showSearch: true,
      optionFilterProp: "label",
      options: getDictOptions(DICT_TYPE.OCE_HCP_ROLE, 'string'),
    },
    suffix: ({model}: RenderCallbackParams): string => {
      if (model.isOrigin == 2) {
        return ''
      } else {
        let var1 = model?.hcpPositionCode
        let var2 = model?.hcp?.hcpPositionCode
        if (var1 == var2) return ''
        return h('span', { style: { color: '#ff6c00'} }, '?') as any
      }
    },
    colProps: { span: 12 }
  },
  {
    label: '类型',
    field: 'hcpTypeCode',
    component: 'Select',
    ifShow: ({ model }) => model.isOrigin == 2,
    componentProps: {
      showSearch: true,
      optionFilterProp: "label",
      options: getDictOptions(DICT_TYPE.OCE_HCP_TYPE, 'string'),
    },
    colProps: { span: 12 }
  },
  {
    // 一定要加，否则获取不到isOrigin属性
    label: 'isOrigin',
    field: 'isOrigin',
    show: false,
    component: 'Input',
    colProps: { span: 12 }
  },
  // 有问题，注释掉
  // {
  //   label: '合并ID',
  //   field: 'mergeOneKeyId',
  //   ifShow: ({ model }) => model.isOrigin == 2,
  //   component: 'Select',
  //   componentProps: {
  //     showSearch: true,
  //     onSearch: (value) => {
  //       let data = getByHcoName(value, '')
  //       optionA.value = data.map(item => ({
  //         value: item.hcoId,
  //         label: item.hcoName,
  //       }))
  //     },
  //     option: optionA,
  //     // labelField: 'hcoName',
  //     // valueField: 'hcoId',
  //   },
  //   helpMessage: "需要合并的另一个医院/医生,适用于重复/多点执业"
  // },
  {
    label: '审核状态',
    field: 'approvalStatus',
    required: true,
    ifShow: ({ model }) => model.isOrigin == 2,
    component: 'Select',
    componentProps: {
      options: [
        { label: '批准', value: 'Approved' },
        { label: '拒绝', value: 'Rejected' },
      ],
    },
    helpMessage: "拒绝 不会修改原数据",
    colProps: { span: 12 }
  },
  {
    label: '常用拒绝备注',
    field: 'approvalRemarks1',
    ifShow: ({ model }) => model.isOrigin == 2,
    component: 'Select',
    componentProps: {
      showSearch: true,
      optionFilterProp: "label",
      options: [
        {
            "label": "无",
            "value": "-"
        },
        {
            "label": "R02A 信息无效（此院无此人）-此院无此人",
            "value": "R02A 信息无效（此院无此人）-此院无此人"
        },
        {
            "label": "R10E 信息无效（超出可验证范围）-更新信息不符",
            "value": "R10E 信息无效（超出可验证范围）-更新信息不符"
        },
        {
            "label": "R03C 信息不全（请提供有效姓名）-提交的医生姓名不规范请重新提交，实际医生姓名为",
            "value": "R03C 信息不全（请提供有效姓名）-提交的医生姓名不规范请重新提交，实际医生姓名为"
        },
        {
          "label": "R02B 信息无效（科室无此人，医院行政科室无法确定其他科室是否有此人）-科室无此人",
          "value": "R02B 信息无效（科室无此人，医院行政科室无法确定其他科室是否有此人）-科室无此人"
        },
        {
          "label": "R02B 信息无效（科室无此人，医院行政科室无法确定其他科室是否有此人）-实际科室为",
          "value": "R02B 信息无效（科室无此人，医院行政科室无法确定其他科室是否有此人）-实际科室为"
        },
        {
          "label": "该医生是多点执业，现推送新的医生ID",
          "value": "该医生是多点执业，现推送新的医生ID"
        },
        {
            "label": "R03C 信息不全（请提供有效姓名）-请重新发起新建医生流程，不可以在原医生上更新新医生信息",
            "value": "R03C 信息不全（请提供有效姓名）-请重新发起新建医生流程，不可以在原医生上更新新医生信息"
        },
        {
            "label": "R10E 信息无效（超出可验证范围）-多点执业医生，不在提出的医院，实际医院为",
            "value": "R10E 信息无效（超出可验证范围）-多点执业医生，不在提出的医院，实际医院为"
        },
        {
          "label": "R10E 信息无效（超出可验证范围）-提交医生变更信息未验证到（未离职/未退休/未转院等）",
          "value": "R10E 信息无效（超出可验证范围）-提交医生变更信息未验证到（未离职/未退休/未转院等）"
        },
        {
            "label": "R10E 信息无效（超出可验证范围）-提交医生验证时 医院、科室、姓名三者有其一与实际验证不同",
            "value": "R10E 信息无效（超出可验证范围）-提交医生验证时 医院、科室、姓名三者有其一与实际验证不同"
        },
      ],
    },
    helpMessage: "常用拒绝备注 审核备注 会合并在一起为审核备注",
    colProps: { span: 24 }
  },
  {
    label: '审核备注',
    field: 'approvalRemarks',
    ifShow: ({ model }) => model.isOrigin == 2,
    component: 'InputTextArea',
    helpMessage: "批准不填；拒绝必填",
    colProps: { span: 24 }
  },
  {
      label: '同名提醒',
      field: 'other',
      ifShow: ({ model }) => model.isOrigin == 2,
      component: 'Input',
      render({ model }){
        return model.other>0?h('span', { style: { color: '#ff6c00'} }, '存在姓名相同，但医院或科室不同的情况，请核实是否为 多点执业'):''
      },
      colProps: { span: 24 }
    },
  {
    label: '注意',
    field: 'xxxx',
    ifShow: ({ model }) => model.isOrigin == 2,
    component: 'Input',
    render(){
      return h('span', { style: { color: 'red' } }, '以下是非必填，如选择 2重复/3多点执业 则必须选择 关联对象；选择操作类型时 审核状态=批准')
    },
    colProps: { span: 24 }
  },
  {
    label: '操作类型',
    field: 'operationType',
    ifShow: ({ model }) => model.isOrigin == 2,
    component: 'Select',
    componentProps: {
      options: [
        { label: '1.转院', value: '1' },
        { label: '2.重复', value: '2' },
        { label: '3.多点执业', value: '3' },
        { label: '4.无效', value: '4' },
      ],
    },
    helpMessage: "选择时 审核状态=批准。如果默认显示 重复，需要代表核实下方管理对象是否正确后再提交",
    colProps: { span: 12 }
  },
]

export const departmentFormSchema: FormSchema[] = [
  {
    label: '上次核查时间',
    field: 'lastDcrTime',
    component: 'Input',
    render({ model }){
      return h('span', { style: { color: 'red', fontWeight: 'bold' } }, useRender.renderDate(model.lastDcrTime as string))
    },
    colProps: { span: 12 }
  },
  {
    label: 'taskId',
    field: 'taskId',
    show: false,
    component: 'Input',
    colProps: { span: 12 }
  },
  {
    label: '科室ID',
    field: 'departmentId',
    show: false,
    component: 'Input',
    colProps: { span: 12 }
  },
  {
    label: '医院名称',
    field: 'hcoName',
    component: 'Input',
    render({ model }){
      return model.hcoName
    },
    colProps: { span: 12 }
  },
  {
    label: '科室名称',
    field: 'departmentName',
    component: 'Input',
    render({ model }){
      return model.departmentName
    },
    colProps: { span: 12 }
  },
  {
    label: 'MDM科室名称',
    field: 'mdmDepartmentName',
    required: true,
    component: 'Select',
    componentProps: {
      showSearch: true,
      optionFilterProp: "label",
      options: getDictOptions(DICT_TYPE.OCE_DEPARTMENT, 'string'),
    },
    colProps: { span: 12 }
  },
  {
    label: '科室专长',
    field: 'departmentSpecialtyCode',
    component: 'Select',
    componentProps: {
      showSearch: true,
      optionFilterProp: "label",
      options: getDictOptions(DICT_TYPE.OCE_HCP_SPECIALTY, 'string'),
    },
    colProps: { span: 12 }
  },
  {
    label: '科室专长2',
    field: 'departmentSpecialtyCode2',
    component: 'Select',
    componentProps: {
      showSearch: true,
      optionFilterProp: "label",
      options: getDictOptions(DICT_TYPE.OCE_HCP_SPECIALTY, 'string'),
    },
    colProps: { span: 12 }
  },
  {
    label: '科室专长3',
    field: 'departmentSpecialtyCode3',
    component: 'Select',
    componentProps: {
      showSearch: true,
      optionFilterProp: "label",
      options: getDictOptions(DICT_TYPE.OCE_HCP_SPECIALTY, 'string'),
    },
    colProps: { span: 12 }
  },
  {
    label: '状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      showSearch: true,
      optionFilterProp: "label",
      options: getDictOptions(DICT_TYPE.OCE_HCO_STATUS, 'string'),
    },
    colProps: { span: 12 }
  },
  {
    label: '审核状态',
    field: 'approvalStatus',
    required: true,
    component: 'Select',
    componentProps: {
      options: [
        { label: '批准', value: 'Approved' },
        { label: '拒绝', value: 'Rejected' },
      ],
    },
    helpMessage: "拒绝 不会修改原数据",
    colProps: { span: 12 }
  },
  {
    label: '常用拒绝备注',
    field: 'approvalRemarks1',
    required: true,
    component: 'Select',
    componentProps: {
      showSearch: true,
      optionFilterProp: "label",
      options: [
        { label: '无', value: '-' },
        { label: 'R10E 信息无效（超出可验证范围）', value: 'R10E 信息无效（超出可验证范围）-' },
        { label: 'R02A 信息无效（此院无此人）', value: 'R02A 信息无效（此院无此人）-' },
        { label: 'R02B 信息无效（科室无此人，医院行政科室无法确定其他科室是否有此人）', value: 'R02B 信息无效（科室无此人，医院行政科室无法确定其他科室是否有此人）-' },
        { label: 'R03C 信息不全（请提供有效姓名）', value: 'R03C 信息不全（请提供有效姓名）-' },
        { label: 'R03B 信息不全（请提供有效科室名或专长）', value: 'R03B 信息不全（请提供有效科室名或专长）-' },
        { label: 'R03C 信息不全（请提供有效姓名）', value: 'R03C 信息不全（请提供有效姓名）-' },
      ],
    },
    helpMessage: "常用拒绝备注 审核备注 会合并在一起为审核备注",
    colProps: { span: 12 }
  },
  {
    label: '审核备注',
    required: true,
    field: 'approvalRemarks',
    component: 'InputTextArea',
    helpMessage: "审核备注和核验的来源，如网址",
    colProps: { span: 12 }
  },
]


