import { defHttp } from '@/utils/http/axios'
import { formatToDateTime } from '@/utils/dateUtil'


// 国际化价格配置
export function createProduct(params) {
  return defHttp.post({ url: '/appLangs/createProduct', params })
}
// 订阅链接日志
export function subLogPage(params) {
  return defHttp.get({ url: '/medsciUsers/subLogPage', params })
}
// 订单列表
export function subOrderPage(params) {
  return defHttp.get({ url: '/medsciUsers/subOrderPage', params })
}
// 事件日志
export function eventLogPage(params) {
  return defHttp.get({ url: '/medsciUsers/eventLogPage', params })
}

// 事件日志
export function statistic(params) {
  return defHttp.get({ url: '/infra/api-access-log/statistic', params })
}

// 更新价格 
export function updatePriceId(params) {
  return defHttp.post({ url: '/appLangs/updatePriceId', params })
}

// 初始化国际产品及价格
export function initProject(params) {
  return defHttp.post({ url: '/appLangs/initProject', params })
}

// 订阅套餐
export function userPackagePage(params) {
  return defHttp.get({ url: '/medsciUsers/userPackagePage', params })
}

// 过期用户套餐
export function expireUserPackage(params) {
  return defHttp.post({ url: '/medsciUsers/expireUserPackage?id=' + params.id })
}

// 用户应用
export function userAppPage(params) {
  return defHttp.get({ url: '/medsciUsers/userAppPage', params })
}

// 过期用户应用
export function expireUserApp(params) {
  return defHttp.post({ url: '/medsciUsers/expireUserApp?id=' + params.id })
}

// 知识库
export function knowledgePage(params) {
  return defHttp.get({ url: '/medsciUsers/knowledgePage', params })
}

// 下载
export function downloadFile(params) {
  const timestamp = formatToDateTime(new Date(), 'YYYYMMDDHHmmss')
  return defHttp.download({ url: '/medsciUsers/downloadFile', params }, `${params.userName}_${params.fileName}_${timestamp}.zip`)
}

// 导入套餐
export function importPackage(params) {
  return defHttp.post({ url: '/medsciUsers/importPackage', params })
}
