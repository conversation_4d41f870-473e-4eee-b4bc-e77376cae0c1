package cn.iocoder.yudao.aiBase.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.iocoder.yudao.aiBase.config.BaseConstant;
import cn.iocoder.yudao.aiBase.dto.request.dify.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.netty.channel.ChannelOption;
import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.ssl.util.InsecureTrustManagerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.netty.http.client.HttpClient;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

import java.util.*;

public class DifyUtil {

    public static final String STREAMING_MODE = "streaming";

    /**post 发送消息*/
    public static final String ChatMsg = "v1/chat-messages";

    /**post 停止*/
    public static final String StopChat = "v1/chat-messages/:task_id/stop";
    public static final String StopChat1 = "v1/workflows/tasks/:task_id/stop";
    public static final String StopChat3 = "v1/completion-messages/:task_id/stop";

    /**post 消息反馈（点赞）*/
    public static final String Feedback = "v1/messages/:message_id/feedbacks";

    /**get 获取下一轮建议问题列表*/
    public static final String Suggested = "v1/messages/:message_id/suggested";

    /**get 获取会话历史消息*/
    public static final String Messages = "v1/messages";

    /**get 获取会话列表*/
    public static final String Conversations = "v1/conversations";

    /**delete 删除会话*/
    public static final String DeleteConverse = "v1/conversations/:conversation_id";

    /**post 会话重命名*/
    public static final String RenameConverse = "v1/conversations/:conversation_id/name";

    /**get 获取应用配置信息*/
    public static final String Parameters = "v1/parameters";

    /**工作流执行*/
    public static final String WorkflowsRun = "v1/workflows/run";

    /**文本完成*/
    public static final String CompletionMsg = "v1/completion-messages";

    /**meta信息*/
    public static final String Meta = "v1/meta";

    /**文件上传*/
    public static final String FilesUpload = "v1/files/upload";

    public static final List<String> NO_FILTER_NAMES = List.of("SHOW", "RESULT", "DETAIL");

    public static JSONObject chatMsg(String token, ChatMessagesRequest param, String host, Boolean addLog) {
        String url = host + ChatMsg;
        String p = JSON.toJSONString(param);
        JSONObject pJson = JSONObject.parseObject(p);
        pJson.put("conversation_id", param.getConversation_id());
        JSONObject res = HttpRequestUtil.post(url, addLog, pJson, HttpRequestUtil.addHeader(HttpRequestUtil.addHeader(), HttpHeaders.AUTHORIZATION,BaseConstant.BEARER + token));
        return res;
    }

    public static JSONObject stopChat(String token, StopChatRequest param, String host, Boolean addLog) {
        String url = host + StopChat.replace(":task_id", param.getTask_id());
        if ("workflow".equals(param.getMode())) {
            url = host + StopChat1.replace(":task_id", param.getTask_id());
        }
        if ("completion".equals(param.getMode())) {
            url = host + StopChat3.replace(":task_id", param.getTask_id());
        }
        String p = JSON.toJSONString(param);
        JSONObject res = HttpRequestUtil.post(url, addLog, JSONObject.parseObject(p), HttpRequestUtil.addHeader(HttpRequestUtil.addHeader(), HttpHeaders.AUTHORIZATION,BaseConstant.BEARER + token));
        return res;
    }

    public static JSONObject feedback(String token, FeedbackRequest param, String host, Boolean addLog) {
        String url = host + Feedback.replace(":message_id", param.getMessage_id());
        String p = JSON.toJSONString(param);
        JSONObject res = HttpRequestUtil.post(url, addLog, JSONObject.parseObject(p), HttpRequestUtil.addHeader(HttpRequestUtil.addHeader(), HttpHeaders.AUTHORIZATION,BaseConstant.BEARER + token));
        return res;
    }

    public static JSONObject suggested(String token, SuggestedRequest param, String host, Boolean addLog) {
        String url = host + Suggested.replace(":message_id", param.getMessage_id()) + "?user="+getUser(param.getUser());
        JSONObject res = HttpRequestUtil.get(url, addLog, HttpRequestUtil.addHeader(HttpRequestUtil.addHeader(), HttpHeaders.AUTHORIZATION,BaseConstant.BEARER + token));
        return res;
    }

    public static JSONObject messages(String token, MessagesRequest param, String host, Boolean addLog) {
        String url = host + Messages + "?user="+getUser(param.getUser())+"&first_id="+param.getFirst_id()+"&limit="+param.getLimit() + "&conversation_id="+param.getConversation_id();
        JSONObject res = HttpRequestUtil.get(url, addLog, HttpRequestUtil.addHeader(HttpRequestUtil.addHeader(), HttpHeaders.AUTHORIZATION,BaseConstant.BEARER + token));
        return res;
    }

    public static JSONObject conversations(String token, ConversationsRequest param, String host, Boolean addLog) {
        String url = host + Conversations + "?user="+getUser(param.getUser())+"&last_id="+param.getLast_id()+"&limit="+param.getLimit() + "&sort_by="+param.getSort_by();
        JSONObject res = HttpRequestUtil.get(url, addLog, HttpRequestUtil.addHeader(HttpRequestUtil.addHeader(), HttpHeaders.AUTHORIZATION,BaseConstant.BEARER + token));
        return res;
    }

    public static JSONObject deleteConverse(String token, DeleteConversationsRequest param, String host, Boolean addLog) {
        String url = host + DeleteConverse.replace(":conversation_id", param.getConversation_id());
        String p = JSON.toJSONString(param);
        JSONObject res = HttpRequestUtil.delete(url, addLog, JSONObject.parseObject(p), HttpRequestUtil.addHeader(HttpRequestUtil.addHeader(), HttpHeaders.AUTHORIZATION,BaseConstant.BEARER + token));
        return res;
    }

    public static JSONObject renameConverse(String token, RenameRequest param, String host, Boolean addLog) {
        String url = host + RenameConverse.replace(":conversation_id", param.getConversation_id());
        String p = JSON.toJSONString(param);
        JSONObject res = HttpRequestUtil.post(url, addLog, JSONObject.parseObject(p), HttpRequestUtil.addHeader(HttpRequestUtil.addHeader(), HttpHeaders.AUTHORIZATION,BaseConstant.BEARER + token));
        return res;
    }

    public static JSONObject parameters(String token, DifyBaseRequest param, String host, Boolean addLog) {
        String url = host + Parameters + "?user=" + getUser(param.getUser());
        JSONObject res = HttpRequestUtil.get(url, addLog, HttpRequestUtil.addHeader(HttpRequestUtil.addHeader(), HttpHeaders.AUTHORIZATION,BaseConstant.BEARER + token));
        return res;
    }

    public static JSONObject workflowsRun(String token, WorkflowsRunRequest param, String host, Boolean addLog) {
        String url = host + WorkflowsRun;
        String p = JSON.toJSONString(param);
        JSONObject pJson = JSONObject.parseObject(p);
        JSONObject res = HttpRequestUtil.post(url, addLog, pJson, HttpRequestUtil.addHeader(HttpRequestUtil.addHeader(), HttpHeaders.AUTHORIZATION,BaseConstant.BEARER + token));
        return res;
    }

    public static JSONObject meta(String token, DifyBaseRequest param, String host, Boolean addLog) {
        String url = host + Meta + "?user="+ getUser(param.getUser());
        JSONObject res = HttpRequestUtil.get(url, addLog, HttpRequestUtil.addHeader(HttpRequestUtil.addHeader(), HttpHeaders.AUTHORIZATION,BaseConstant.BEARER + token));
        return res;
    }

    public static JSONObject completionMsg(String token, WorkflowsRunRequest param, String host, Boolean addLog) {
        String url = host + CompletionMsg;
        String p = JSON.toJSONString(param);
        JSONObject pJson = JSONObject.parseObject(p);
        JSONObject res = HttpRequestUtil.post(url, addLog, pJson, HttpRequestUtil.addHeader(HttpRequestUtil.addHeader(), HttpHeaders.AUTHORIZATION,BaseConstant.BEARER + token));
        return res;
    }

    public static String getUser(String user) {
        try {
            return URLEncoder.encode(user, StandardCharsets.UTF_8.toString());
        } catch (UnsupportedEncodingException e) {
            return user;
        }
    }

    public static JSONObject filesUpload(String token, FilesUploadRequest param, String host, Boolean addLog) {
        String url = host + FilesUpload;

        // 发送POST请求并上传文件
        HttpResponse response = HttpRequest.post(url)
            .header(HttpHeaders.AUTHORIZATION, BaseConstant.BEARER + token)
            .header(HttpHeaders.CONTENT_TYPE, "multipart/form-data")
            .form("user", param.getUser())
            .form("file", CommonUtil.convertTo(param.getFile()))
            .timeout(60000)
            .execute();
        JSONObject res = JSONObject.parseObject(response.body());
        return res;
    }

    /**
     * 获取不需要ssl检验的WebClient
     * @param baseUrl
     * @return
     */
    public static WebClient getWebClient(String baseUrl, Boolean hasFile) {
        try {
            // 免除 SSL 校验
            SslContext sslContext = SslContextBuilder.forClient()
                .trustManager(InsecureTrustManagerFactory.INSTANCE)
                .build();

            // 创建 ExchangeStrategies 并设置较大的响应体缓冲限制
            int maxResponseSize = hasFile ? 20 * 1024 * 1024 : 15 * 1024 * 1024;
            ExchangeStrategies strategies = ExchangeStrategies.builder()
                .codecs(configurer -> {
                    configurer.defaultCodecs().maxInMemorySize(maxResponseSize);
                    // 禁用缓冲以支持真正的流式传输
                    configurer.defaultCodecs().enableLoggingRequestDetails(true);
                })
                .build();

            HttpClient httpClient = HttpClient.create()
                .secure(t -> t.sslContext(sslContext))
                // 配置连接池以支持流式传输
                .option(ChannelOption.SO_KEEPALIVE, true)
                .option(ChannelOption.TCP_NODELAY, true);

            return WebClient.builder()
                .exchangeStrategies(strategies)
                .baseUrl(baseUrl)
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .build();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取sse数据流
     * @param uri
     * @param pJson
     * @param token
     * @return
     */
    public static Flux<ServerSentEvent> getFlux(String host, String uri, JSONObject pJson, String token, Boolean hasFile) {
        try {
            WebClient webClient = getWebClient(host, hasFile);

            return webClient.post()
                .uri(uri)
                .contentType(MediaType.APPLICATION_JSON)
                .header(HttpHeaders.AUTHORIZATION, BaseConstant.BEARER + token)
                .body(BodyInserters.fromValue(pJson))
                .accept(MediaType.TEXT_EVENT_STREAM)
                .retrieve()
                // 使用bodyToFlux(String.class)来获取原始SSE文本流
                .bodyToFlux(String.class)
                .filter(Objects::nonNull)
                .filter(line -> !line.trim().isEmpty())
                // 解析SSE格式的数据
                .map(DifyUtil::parseSseEvent)
                .filter(Objects::nonNull)
                // 应用业务过滤逻辑
                .map(sse -> filterNode(sse.data()))
                // 添加背压控制，防止数据积压
                .onBackpressureBuffer(1000)
                .onErrorResume(e -> {
                    e.printStackTrace();
                    return Flux.error(new RuntimeException("Failed to relay SSE events, Please contact the administrator for assistance.", e));
                });
        } catch (Exception e) {
            e.printStackTrace();
            return Flux.empty();
        }
    }

    /**
     * 解析SSE事件格式的文本行
     * @param line SSE格式的文本行
     * @return ServerSentEvent对象
     */
    public static ServerSentEvent parseSseEvent(String line) {
        if (line == null || line.trim().isEmpty()) {
            return null;
        }

        // 处理SSE格式：data: {...}
        if (line.startsWith("data: ")) {
            String data = line.substring(6); // 移除"data: "前缀
            if ("[DONE]".equals(data.trim())) {
                // 处理结束标记
                return ServerSentEvent.builder().data("").build();
            }

            // 尝试解析JSON数据，保持原始数据结构
            try {
                JSONObject.parseObject(data); // 验证是否为有效JSON
                return ServerSentEvent.builder().data(data).build();
            } catch (Exception e) {
                // 如果不是JSON格式，直接返回原始数据
                return ServerSentEvent.builder().data(data).build();
            }
        }

        // 处理其他SSE字段（如event:, id:等）
        if (line.startsWith("event: ") || line.startsWith("id: ") || line.startsWith("retry: ")) {
            // 暂时忽略这些字段，只处理data字段
            return null;
        }

        // 如果是空行，表示事件结束
        if (line.trim().isEmpty()) {
            return null;
        }

        // 如果不是标准SSE格式，尝试直接作为数据处理
        return ServerSentEvent.builder().data(line).build();
    }

    public static ServerSentEvent filterNode(Object eventData) {
        String str = "{}";
        if (eventData == null) {
            // 返回空对象
        } else {
            // 如果eventData已经是字符串，直接使用，避免重复序列化
            if (eventData instanceof String) {
                str = (String) eventData;
            } else {
                str = JSONObject.toJSONString(eventData);
            }

            // 只有当字符串是有效JSON时才进行处理
            try {
                if (str.contains("\"node_started\"")) {
                    JSONObject json = JSONObject.parseObject(str);
                    JSONObject data = json.getJSONObject("data");
                    if (data != null && data.containsKey("title") && !containsNodeName(data.getString("title"))) {                        data.put("inputs", null);
                        json.put("data", data);
                        str = json.toJSONString();
                    }
                }
                if (str.contains("\"node_finished\"")) {
                    JSONObject json = JSONObject.parseObject(str);
                    JSONObject data = json.getJSONObject("data");
                    if (data != null && data.containsKey("title") && !containsNodeName(data.getString("title"))) {                        data.put("inputs", null);
                        data.put("process_data", null);
                        data.put("error", null);
                        data.put("execution_metadata", null);
                        if (!"智能体推理思维链".equals(data.getString("title"))) {
                            data.put("outputs", null);
                        }
                        json.put("data", data);
                        str = json.toJSONString();
                    }
                    if (data != null && data.containsKey("title") && containsNodeName(data.getString("title"))) {
                        JSONObject processData = data.getJSONObject("process_data");
                        processData.put("prompts",  null);
                        processData.put("usage",  null);
                        data.put("process_data", processData);
                        json.put("data", data);
                        str = json.toJSONString();
                    }
                }
            } catch (Exception e) {
                // 如果JSON解析失败，保持原始字符串
                // 这样可以避免破坏非JSON格式的数据
            }
        }
        ServerSentEvent res = ServerSentEvent.builder().data(str).build();
        return res;
    }

    /**
     * 检查给定的title是否包含在nodeNames列表中
     * @param title 需要检查的标题
     * @return 如果包含返回true，否则返回false
     */
    public static boolean containsNodeName(String title) {
        for (String nodeName : NO_FILTER_NAMES) {
            if (title.toUpperCase().contains(nodeName.toUpperCase())) {
                return true;
            }
        }
        return false;
    }


}
